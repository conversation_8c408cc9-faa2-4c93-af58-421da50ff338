import ytdl from '@distube/ytdl-core';

// Helper function to download and parse subtitle content
const downloadAndParseSubtitles = async (subtitleUrl) => {
  try {
    const response = await fetch(subtitleUrl);
    const content = await response.text();
    
    // Parse VTT format
    if (content.includes('WEBVTT')) {
      return parseVTTContent(content);
    }
    
    // Try to parse as JSON3 format (YouTube's format)
    try {
      const jsonData = JSON.parse(content);
      if (jsonData.events) {
        return parseJSON3Content(jsonData);
      }
    } catch (e) {
      // Not JSON, continue with VTT parsing
    }
    
    // Fallback to VTT parsing
    return parseVTTContent(content);
  } catch (error) {
    console.error('Error downloading/parsing subtitles:', error);
    return [];
  }
};

// Parse VTT subtitle format
const parseVTTContent = (content) => {
  const subtitles = [];
  const lines = content.split('\n');
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    
    // Look for timestamp lines (format: 00:00:00.000 --> 00:00:03.000)
    if (line.includes('-->')) {
      const [startTime, endTime] = line.split('-->').map(t => t.trim());
      const start = parseVTTTime(startTime);
      const end = parseVTTTime(endTime);
      
      // Get the text lines that follow
      const textLines = [];
      i++; // Move to next line after timestamp
      
      while (i < lines.length && lines[i].trim() !== '' && !lines[i].includes('-->')) {
        const textLine = lines[i].trim();
        if (textLine) {
          // Clean up any VTT formatting tags
          const cleanText = textLine.replace(/<[^>]*>/g, '').trim();
          if (cleanText) {
            textLines.push(cleanText);
          }
        }
        i++;
      }
      i--; // Step back one since the loop will increment
      
      if (textLines.length > 0) {
        subtitles.push({
          start,
          end,
          text: textLines.join(' ')
        });
      }
    }
  }
  
  return subtitles;
};

// Parse JSON3 subtitle format (YouTube's format)
const parseJSON3Content = (jsonData) => {
  const subtitles = [];
  
  if (jsonData.events) {
    for (const event of jsonData.events) {
      if (event.segs) {
        let text = '';
        for (const seg of event.segs) {
          if (seg.utf8) {
            text += seg.utf8;
          }
        }
        
        if (text.trim()) {
          subtitles.push({
            start: event.tStartMs / 1000,
            end: (event.tStartMs + event.dDurationMs) / 1000,
            text: text.trim()
          });
        }
      }
    }
  }
  
  return subtitles;
};

// Parse VTT time format to seconds
const parseVTTTime = (timeStr) => {
  const parts = timeStr.split(':');
  if (parts.length === 3) {
    const hours = parseInt(parts[0]);
    const minutes = parseInt(parts[1]);
    const seconds = parseFloat(parts[2]);
    return hours * 3600 + minutes * 60 + seconds;
  }
  return 0;
};

export default async (req, res) => {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method !== 'GET') {
    res.status(405).json({ error: 'Method not allowed' });
    return;
  }

  try {
    const { params } = req.query;
    const [videoId, langCode] = params;
    
    if (!videoId || !langCode) {
      return res.status(400).json({ error: 'Video ID and language code are required' });
    }

    console.log(`Downloading subtitles for video: ${videoId}, language: ${langCode}`);

    // Get video info and extract subtitles
    const videoInfo = await ytdl.getInfo(`https://www.youtube.com/watch?v=${videoId}`);

    let subtitles = [];
    let subtitleUrl = null;

    // Find the subtitle track for the requested language
    if (videoInfo.player_response?.captions?.playerCaptionsTracklistRenderer?.captionTracks) {
      const captionTracks = videoInfo.player_response.captions.playerCaptionsTracklistRenderer.captionTracks;

      const track = captionTracks.find(track => track.languageCode === langCode);

      if (track) {
        subtitleUrl = track.baseUrl;
        console.log(`Found subtitles for ${langCode}`);
      } else {
        return res.status(404).json({ error: 'Subtitles not found for the specified language' });
      }
    } else {
      return res.status(404).json({ error: 'No subtitles available for this video' });
    }

    // Download and parse subtitles
    if (subtitleUrl) {
      subtitles = await downloadAndParseSubtitles(subtitleUrl);
    }

    res.status(200).json({
      videoId,
      title: videoInfo.videoDetails?.title || 'YouTube Video',
      thumbnail: videoInfo.videoDetails?.thumbnails?.[0]?.url || `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`,
      language: langCode,
      subtitles
    });

  } catch (error) {
    console.error('Error downloading subtitles:', error);
    res.status(500).json({
      error: 'Failed to download subtitles',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};
