import youtubedl from 'youtube-dl-exec';

// Helper function to get language name from code
const getLanguageName = (code) => {
  const languageMap = {
    'en': 'English',
    'es': 'Spanish',
    'fr': 'French',
    'de': 'German',
    'it': 'Italian',
    'pt': 'Portuguese',
    'ru': 'Russian',
    'ja': 'Japanese',
    'ko': 'Korean',
    'zh': 'Chinese',
    'ar': 'Arabic',
    'hi': 'Hindi',
    'tr': 'Turkish',
    'pl': 'Polish',
    'nl': 'Dutch',
    'sv': 'Swedish',
    'da': 'Danish',
    'no': 'Norwegian',
    'fi': 'Finnish',
    'cs': 'Czech',
    'hu': 'Hungarian',
    'ro': 'Romanian',
    'bg': 'Bulgarian',
    'hr': 'Croatian',
    'sk': 'Slovak',
    'sl': 'Slovenian',
    'et': 'Estonian',
    'lv': 'Latvian',
    'lt': 'Lithuanian',
    'uk': 'Ukrainian',
    'el': 'Greek',
    'he': 'Hebrew',
    'th': 'Thai',
    'vi': 'Vietnamese',
    'id': 'Indonesian',
    'ms': 'Malay',
    'tl': 'Filipino',
    'sw': 'Swahili',
    'af': 'Afrikaans'
  };
  
  return languageMap[code] || code.toUpperCase();
};

export default async (req, res) => {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method !== 'GET') {
    res.status(405).json({ error: 'Method not allowed' });
    return;
  }

  try {
    const { videoId } = req.query;
    
    if (!videoId) {
      return res.status(400).json({ error: 'Video ID is required' });
    }

    console.log(`Fetching subtitle languages for video: ${videoId}`);

    // Get video info including available subtitles
    const videoInfo = await youtubedl(`https://www.youtube.com/watch?v=${videoId}`, {
      dumpSingleJson: true,
      noCheckCertificates: true,
      noWarnings: true,
      preferFreeFormats: true,
      addHeader: ['referer:youtube.com', 'user-agent:googlebot']
    });

    const availableLanguages = [];

    if (videoInfo.subtitles) {
      // Add manual subtitles
      Object.keys(videoInfo.subtitles).forEach(langCode => {
        const langName = getLanguageName(langCode);
        availableLanguages.push({
          code: langCode,
          name: langName,
          isAutoGenerated: false
        });
      });
    }

    if (videoInfo.automatic_captions) {
      // Add auto-generated subtitles
      Object.keys(videoInfo.automatic_captions).forEach(langCode => {
        // Don't duplicate if we already have manual subtitles for this language
        if (!availableLanguages.find(lang => lang.code === langCode)) {
          const langName = getLanguageName(langCode);
          availableLanguages.push({
            code: langCode,
            name: langName,
            isAutoGenerated: true
          });
        }
      });
    }

    if (availableLanguages.length === 0) {
      return res.status(404).json({ error: 'No subtitles available for this video' });
    }

    res.status(200).json({
      videoId,
      title: videoInfo.title || 'YouTube Video',
      thumbnail: videoInfo.thumbnail || `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`,
      languages: availableLanguages
    });

  } catch (error) {
    console.error('Error fetching subtitle languages:', error);
    res.status(500).json({
      error: 'Failed to fetch subtitle languages',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};
