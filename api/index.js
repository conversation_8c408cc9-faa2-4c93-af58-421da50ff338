const { readFileSync } = require('fs');
const { join } = require('path');

module.exports = (req, res) => {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  try {
    // Serve the React app's index.html
    const indexPath = join(process.cwd(), 'dist', 'client', 'index.html');
    const indexHtml = readFileSync(indexPath, 'utf8');
    
    res.setHeader('Content-Type', 'text/html');
    res.status(200).send(indexHtml);
  } catch (error) {
    console.error('Error serving index.html:', error);
    res.status(500).json({ error: 'Failed to serve application' });
  }
};
